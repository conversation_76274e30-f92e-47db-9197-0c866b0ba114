<!-- SWAAGAT Landing Page Component -->
<div class="page-layout">
  <!-- Header Section -->
  <header class="header">
    <div class="container">
      <div class="header-content">
        <div class="logo">
          <img src="assets/images/swaagat-logo.png" alt="SWAAGAT Logo" class="logo-img">
          <div class="logo-text">
            <h1>SWAAGAT</h1>
            <p>Government of Tripura</p>
          </div>
        </div>
        <nav class="nav" [class.active]="mobileMenuOpen">
          <a href="#" class="nav-link" (click)="toggleMobileMenu()">Home</a>
          <a href="#about" class="nav-link" (click)="toggleMobileMenu()">About Us</a>
          <a href="#services" class="nav-link" (click)="toggleMobileMenu()">Services</a>
          <a href="#department" class="nav-link" (click)="toggleMobileMenu()">Department</a>
          <a href="#land" class="nav-link" (click)="toggleMobileMenu()">Land</a>
          <a href="#kya" class="nav-link" (click)="toggleMobileMenu()">KYA</a>
          <a href="#acts-rules" class="nav-link" (click)="toggleMobileMenu()">Acts & Rules</a>
          <a href="#nsws" class="nav-link" (click)="toggleMobileMenu()">NSWS</a>
          <button mat-raised-button color="primary" routerLink="/auth/login" class="cta-button">Login</button>
        </nav>
        <button class="mobile-menu-toggle" (click)="toggleMobileMenu()">
          <mat-icon>{{ mobileMenuOpen ? 'close' : 'menu' }}</mat-icon>
        </button>
      </div>
    </div>
  </header>

  <!-- Hero Banner (Carousel) Section -->
  <section class="banner-section">
    <div class="banner-container relative">
      <div class="banner-slides" [style.transform]="'translateX(-' + (currentSlide * 100) + '%)'">
        <div class="banner-slide" *ngFor="let slide of carouselSlides">
          <img [src]="slide.image" [alt]="slide.title" class="banner-img">
          <div class="banner-overlay"></div>
          <div class="banner-content">
            <h1 class="banner-title">{{ slide.title }}</h1>
            <p class="banner-subtitle">{{ slide.subtitle }}</p>
            <div class="banner-actions">
              <button mat-raised-button color="accent" [routerLink]="slide.buttonLink" class="banner-button">
                {{ slide.buttonText }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- Carousel Controls -->
      <div class="carousel-controls">
        <button mat-icon-button (click)="prevSlide()" class="carousel-btn prev">
          <mat-icon>chevron_left</mat-icon>
        </button>
        <button mat-icon-button (click)="nextSlide()" class="carousel-btn next">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
      <!-- Carousel Indicators -->
      <div class="carousel-indicators">
        <button *ngFor="let slide of carouselSlides; let i = index" (click)="goToSlide(i)"
                [class.active]="i === currentSlide" class="indicator"></button>
      </div>
    </div>
  </section>

  <!-- About SWAAGAT Section -->
  <section id="about" class="about-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">About SWAAGAT</h2>
        <p class="section-subtitle">Investment Promotion Agency Tripura (IPAT)</p>
        <p class="section-description">
          SWAAGAT, powered by the Investment Promotion Agency Tripura (IPAT), is the state's nodal platform for investment promotion, facilitating seamless business setup through digital innovation and transparent processes.
        </p>
      </div>
      <div class="features-grid">
        <mat-card class="feature-card">
          <mat-card-content>
            <div class="feature-icon">
              <mat-icon>view_module</mat-icon>
            </div>
            <h3 class="feature-title">Single-Window System</h3>
            <p class="feature-description">One platform for all investment approvals and clearances.</p>
          </mat-card-content>
        </mat-card>
        <mat-card class="feature-card">
          <mat-card-content>
            <div class="feature-icon">
              <mat-icon>cloud</mat-icon>
            </div>
            <h3 class="feature-title">Digital-First Process</h3>
            <p class="feature-description">Streamlined online applications with real-time tracking.</p>
          </mat-card-content>
        </mat-card>
        <mat-card class="feature-card">
          <mat-card-content>
            <div class="feature-icon">
              <mat-icon>support_agent</mat-icon>
            </div>
            <h3 class="feature-title">Dedicated Support</h3>
            <p class="feature-description">Personal investment facilitation and guidance.</p>
          </mat-card-content>
        </mat-card>
        <mat-card class="feature-card">
          <mat-card-content>
            <div class="feature-icon">
              <mat-icon>verified</mat-icon>
            </div>
            <h3 class="feature-title">Transparent Governance</h3>
            <p class="feature-description">Clear timelines and accountability at every step.</p>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="mission-statement">
        <h3 class="mission-title">Our Mission</h3>
        <p class="mission-text italic">
          "To establish Tripura as the preferred investment destination in Northeast India through innovative digital solutions, policy reforms, and world-class investor services."
        </p>
      </div>
    </div>
  </section>

  <!-- Advantages Section -->
  <section id="advantages" class="advantages-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Key Benefits of Investing in Tripura</h2>
        <p class="section-subtitle">Why Choose Tripura for Your Business</p>
      </div>
      <div class="advantages-grid">
        <mat-card class="advantage-card">
          <mat-card-content>
            <div class="advantage-icon">
              <mat-icon>timer</mat-icon>
            </div>
            <h3 class="advantage-title">Reduced Approval Time</h3>
            <p class="advantage-description">Approval times reduced by 60% with streamlined processes.</p>
          </mat-card-content>
        </mat-card>
        <mat-card class="advantage-card">
          <mat-card-content>
            <div class="advantage-icon">
              <mat-icon>contact_support</mat-icon>
            </div>
            <h3 class="advantage-title">Single-Point Contact</h3>
            <p class="advantage-description">Single-point contact for all clearances.</p>
          </mat-card-content>
        </mat-card>
        <mat-card class="advantage-card">
          <mat-card-content>
            <div class="advantage-icon">
              <mat-icon>track_changes</mat-icon>
            </div>
            <h3 class="advantage-title">Real-Time Tracking</h3>
            <p class="advantage-description">Monitor your application status in real-time.</p>
          </mat-card-content>
        </mat-card>
        <mat-card class="advantage-card">
          <mat-card-content>
            <div class="advantage-icon">
              <mat-icon>card_giftcard</mat-icon>
            </div>
            <h3 class="advantage-title">Investment Incentives</h3>
            <p class="advantage-description">Comprehensive incentives including subsidies and tax holidays.</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="services-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Services in SWAAGAT</h2>
        <p class="section-subtitle">Comprehensive Digital Tools for Your Investment Journey</p>
      </div>
      <div class="services-grid">
        <mat-card class="service-card" *ngFor="let service of services">
          <mat-card-content>
            <div class="service-icon">
              <i [class]="'feather-' + service.icon"></i>
            </div>
            <h3 class="service-title">{{ service.title }}</h3>
            <p class="service-description">{{ service.description }}</p>
            <ul class="service-features">
              <li *ngFor="let feature of service.features">{{ feature }}</li>
            </ul>
            <button mat-raised-button color="accent" [routerLink]="service.buttonLink" class="service-button">Learn More</button>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </section>

  <!-- Investment Journey Section -->
  <section id="journey" class="journey-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Investment Journey</h2>
        <p class="section-subtitle">Navigate Your Investment Process in 4 Simple Steps</p>
      </div>
      <div class="stepper-container">
        <mat-stepper [orientation]="isMobile ? 'vertical' : 'horizontal'" class="investment-stepper">
          <mat-step *ngFor="let step of investmentSteps; let i = index" [label]="step.label">
            <div class="step-content">
              <div class="step-number">{{ i + 1 }}</div>
              <h3 class="step-title">{{ step.label }}</h3>
              <p class="step-description">{{ step.description }}</p>
              <ul class="step-features">
                <li *ngFor="let feature of step.features">{{ feature }}</li>
              </ul>
            </div>
          </mat-step>
        </mat-stepper>
      </div>
    </div>
  </section>

  <!-- Investment Sectors Section -->
  <section id="sectors" class="sectors-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Investment Sectors</h2>
        <p class="section-subtitle">Explore Diverse Opportunities Across Tripura's Growth Sectors</p>
      </div>
      <div class="sectors-grid">
        <mat-card class="sector-card" *ngFor="let sector of sectors">
          <mat-card-content>
            <div class="sector-image">
              <img [src]="sector.image" [alt]="sector.title" class="sector-img">
            </div>
            <h3 class="sector-title">{{ sector.title }}</h3>
            <p class="sector-description">{{ sector.description }}</p>
            <div class="sector-stats">
              <div><strong>Investment:</strong> ₹{{ sector.investment }} Cr</div>
              <div><strong>Projects:</strong> {{ sector.projects }}</div>
            </div>
            <div class="sector-opportunities">
              <strong>Key Opportunities:</strong>
              <ul>
                <li *ngFor="let opportunity of sector.opportunities">{{ opportunity }}</li>
              </ul>
            </div>
            <button mat-raised-button color="accent" [routerLink]="sector.buttonLink" class="sector-button">Explore Sector</button>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="cta-section">
        <h3 class="cta-title">Ready to Invest?</h3>
        <p class="cta-description">Join hundreds of successful investors who have chosen Tripura as their investment destination.</p>
        <button mat-raised-button color="primary" routerLink="/investment-guide" class="cta-button">Get Investment Guide</button>
      </div>
    </div>
  </section>

  <!-- Analytics Dashboard Section -->
  <section id="dashboard" class="dashboard-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Live Analytics Dashboard</h2>
        <p class="section-subtitle">Real-Time Insights into Tripura's Investment Landscape</p>
      </div>
      <div class="dashboard-content">
        <div class="dashboard-stats">
          <mat-card class="stat-card" *ngFor="let stat of dashboardStats">
            <mat-card-content>
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-change">{{ stat.change }}</div>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="dashboard-preview">
          <img src="assets/images/dashboard-mockup.png" alt="Analytics Dashboard Mockup" class="dashboard-img">
        </div>
      </div>
    </div>
  </section>

  <!-- Investor Support Section -->
  <section id="support" class="support-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Investor Support</h2>
        <p class="section-subtitle">Comprehensive Support Throughout Your Investment Journey</p>
      </div>
      <div class="support-grid">
        <mat-card class="support-card" *ngFor="let support of supportOptions">
          <mat-card-content>
            <div class="support-icon">
              <mat-icon>{{ support.icon }}</mat-icon>
            </div>
            <h3 class="support-title">{{ support.title }}</h3>
            <p class="support-description">{{ support.description }}</p>
            <button mat-raised-button color="accent" [routerLink]="support.buttonLink" class="support-button">{{ support.buttonText }}</button>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="contact-info">
        <h3 class="contact-title">Contact Information</h3>
        <div class="contact-details">
          <div class="contact-item">
            <strong>Office Address:</strong>
            <p>Investment Promotion Agency Tripura, Secretariat Complex, Civil Secretariat, Agartala, Tripura - 799001</p>
          </div>
          <div class="contact-item">
            <strong>Phone Numbers:</strong>
            <p>Helpline: +91-381-2323456 | Direct: +91-381-2323457 | Toll-free: 1800-345-6789</p>
          </div>
          <div class="contact-item">
            <strong>Email Addresses:</strong>
            <p>General: <EMAIL> | Support: <EMAIL> | Director: <EMAIL></p>
          </div>
          <div class="contact-item">
            <strong>Office Hours:</strong>
            <p>Monday - Friday: 9:00 AM - 6:00 PM | Saturday: 9:00 AM - 2:00 PM | Sunday: Closed</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-brand">
          <h3>SWAAGAT</h3>
          <p>Facilitating investments in Tripura through digital innovation, transparent processes, and dedicated support for a prosperous future.</p>
        </div>
        <div class="footer-links">
          <h4>Quick Links</h4>
          <ul>
            <li><a href="#about">About SWAAGAT</a></li>
            <li><a href="#journey">Investment Process</a></li>
            <li><a href="#services">Key Services</a></li>
            <li><a href="#sectors">Investment Sectors</a></li>
            <li><a href="#support">Support Center</a></li>
            <li><a href="#dashboard">Live Dashboard</a></li>
          </ul>
        </div>
        <div class="footer-links">
          <h4>Legal & Compliance</h4>
          <ul>
            <li><a href="/privacy-policy">Privacy Policy</a></li>
            <li><a href="/terms">Terms of Service</a></li>
            <li><a href="/cookie-policy">Cookie Policy</a></li>
            <li><a href="/accessibility">Accessibility</a></li>
            <li><a href="/rti">RTI</a></li>
            <li><a href="/grievance">Grievance Redressal</a></li>
          </ul>
        </div>
        <div class="footer-contact">
          <h4>Contact Information</h4>
          <p>Civil Secretariat, Agartala, Tripura - 799001, India</p>
          <p>+91-381-2323456</p>
          <p><EMAIL></p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 Investment Promotion Agency Tripura. All rights reserved. | Last updated: December 2024 | Version 2.1</p>
      </div>
    </div>
  </footer>
</div>